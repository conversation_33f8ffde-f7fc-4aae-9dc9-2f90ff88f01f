#!/usr/bin/env python3
"""
SceneLeapPlus 缓存数据集测试和数据分析脚本

该脚本用于：
1. 根据 config/data/sceneleapplus.yaml 配置初始化 SceneLeapPlusDatasetCached
2. 生成缓存文件
3. 加载几个样本进行数据分析
4. 展示数据集的基本统计信息
"""

import os
import sys
import yaml
import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import logging
from typing import Dict, Any, List
import time
from torch.utils.data import DataLoader

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入数据集和处理函数
from datasets.sceneleapplus_cached import SceneLeapPlusDatasetCached
from utils.hand_helper import (
    process_hand_pose, 
    process_hand_pose_test, 
    denorm_hand_pose_robust, 
    decompose_hand_pose,
    JOINT_ANGLE_DIM,
    ROT_DIM_DICT
)

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_config(config_path: str) -> Dict[str, Any]:
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config

def create_dataset_from_config(config: Dict[str, Any], mode: str = "train") -> SceneLeapPlusDatasetCached:
    """根据配置创建数据集"""
    dataset_config = config[mode]
    
    # 使用配置文件中的路径，并设置较小的max_grasps_per_object加快测试
    dataset_params = {
        'root_dir': dataset_config['root_dir'],
        'succ_grasp_dir': dataset_config['succ_grasp_dir'], 
        'obj_root_dir': dataset_config['obj_root_dir'],
        'num_grasps': dataset_config.get('num_grasps', 64),
        # 'mode': dataset_config.get('mode', 'camera_centric'),
        'mode': 'camera_centric_scene_mean_normalized',
        'max_grasps_per_object': dataset_config.get('max_grasps_per_object', 128),
        'mesh_scale': dataset_config.get('mesh_scale', 0.1),
        'num_neg_prompts': dataset_config.get('num_neg_prompts', 4),
        'enable_cropping': dataset_config.get('enable_cropping', True),
        'max_points': dataset_config.get('max_points', 10000),
        'grasp_sampling_strategy': dataset_config.get('grasp_sampling_strategy', 'random'),
        'cache_version': dataset_config.get('cache_version', 'v1.0_plus'),
        'cache_mode': dataset_config.get('cache_mode', mode)
    }
    
    logger.info(f"创建数据集，参数: {dataset_params}")
    return SceneLeapPlusDatasetCached(**dataset_params)

def analyze_sample_data(sample: Dict[str, Any], sample_idx: int) -> Dict[str, Any]:
    """分析单个样本数据"""
    analysis = {
        'sample_idx': sample_idx,
        'has_error': 'error' in sample,
    }
    
    if analysis['has_error']:
        analysis['error_msg'] = sample.get('error', 'Unknown error')
        logger.warning(f"样本 {sample_idx} 包含错误: {analysis['error_msg']}")
        return analysis
    
    # 分析点云数据
    if 'scene_pc' in sample:
        scene_pc = sample['scene_pc']
        analysis['scene_pc_shape'] = scene_pc.shape
        analysis['scene_pc_dtype'] = str(scene_pc.dtype)
        analysis['num_points'] = scene_pc.shape[0] if len(scene_pc.shape) > 0 else 0
        analysis['has_rgb'] = scene_pc.shape[1] == 6 if len(scene_pc.shape) > 1 else False
        
        if analysis['num_points'] > 0:
            # 分析XYZ坐标范围
            xyz = scene_pc[:, :3]
            analysis['xyz_min'] = xyz.min(dim=0)[0].tolist()
            analysis['xyz_max'] = xyz.max(dim=0)[0].tolist()
            analysis['xyz_mean'] = xyz.mean(dim=0).tolist()
            analysis['xyz_std'] = xyz.std(dim=0).tolist()
            
            # 分析RGB值范围（如果有的话）
            if analysis['has_rgb']:
                rgb = scene_pc[:, 3:6]
                analysis['rgb_min'] = rgb.min(dim=0)[0].tolist()
                analysis['rgb_max'] = rgb.max(dim=0)[0].tolist()
                analysis['rgb_mean'] = rgb.mean(dim=0).tolist()
    
    # 分析物体掩码
    if 'object_mask' in sample:
        object_mask = sample['object_mask']
        analysis['object_mask_shape'] = object_mask.shape
        analysis['object_mask_dtype'] = str(object_mask.dtype)
        analysis['num_object_points'] = object_mask.sum().item() if len(object_mask.shape) > 0 else 0
        analysis['object_ratio'] = analysis['num_object_points'] / analysis['num_points'] if analysis['num_points'] > 0 else 0
    
    # 分析抓取姿态数据
    if 'hand_model_pose' in sample:
        hand_pose = sample['hand_model_pose']
        analysis['hand_pose_shape'] = hand_pose.shape
        analysis['hand_pose_dtype'] = str(hand_pose.dtype)
        analysis['num_grasps'] = hand_pose.shape[0] if len(hand_pose.shape) > 0 else 0
        
        if analysis['num_grasps'] > 0:
            analysis['hand_pose_mean'] = hand_pose.mean(dim=0).mean().item()
            analysis['hand_pose_std'] = hand_pose.std().item()
    
    # 分析SE3变换矩阵
    if 'se3' in sample:
        se3 = sample['se3']
        analysis['se3_shape'] = se3.shape
        analysis['se3_dtype'] = str(se3.dtype)
        
        if len(se3.shape) >= 3:
            # 检查SE3矩阵的有效性
            analysis['se3_valid'] = []
            for i in range(se3.shape[0]):
                matrix = se3[i]
                # 检查是否为有效的SE3矩阵（旋转部分应该是正交矩阵）
                rotation_part = matrix[:3, :3]
                det = torch.det(rotation_part)
                is_valid = abs(det - 1.0) < 0.1  # 允许一定误差
                analysis['se3_valid'].append(is_valid)
            
            analysis['num_valid_se3'] = sum(analysis['se3_valid'])
    
    # 分析文本提示
    if 'positive_prompt' in sample:
        analysis['positive_prompt'] = sample['positive_prompt']
        analysis['positive_prompt_length'] = len(sample['positive_prompt'])
    
    if 'negative_prompts' in sample:
        neg_prompts = sample['negative_prompts']
        analysis['num_negative_prompts'] = len(neg_prompts)
        analysis['negative_prompts'] = neg_prompts
        analysis['negative_prompt_lengths'] = [len(p) for p in neg_prompts]
    
    # 分析物体网格数据
    if 'obj_verts' in sample:
        obj_verts = sample['obj_verts']
        analysis['obj_verts_shape'] = obj_verts.shape
        analysis['num_vertices'] = obj_verts.shape[0] if len(obj_verts.shape) > 0 else 0
        
        if analysis['num_vertices'] > 0:
            analysis['obj_verts_min'] = obj_verts.min(dim=0)[0].tolist()
            analysis['obj_verts_max'] = obj_verts.max(dim=0)[0].tolist()
            analysis['obj_verts_mean'] = obj_verts.mean(dim=0).tolist()
    
    if 'obj_faces' in sample:
        obj_faces = sample['obj_faces']
        analysis['obj_faces_shape'] = obj_faces.shape
        analysis['num_faces'] = obj_faces.shape[0] if len(obj_faces.shape) > 0 else 0
    
    # 分析验证/测试特有的字段
    for key in ['obj_code', 'scene_id', 'category_id_from_object_index', 'depth_view_index']:
        if key in sample:
            analysis[key] = sample[key]
    
    return analysis

def print_dataset_summary(dataset: SceneLeapPlusDatasetCached, analyses: List[Dict[str, Any]]):
    """打印数据集摘要信息"""
    print("\n" + "="*80)
    print("SceneLeapPlus 数据集分析摘要")
    print("="*80)
    
    # 基本信息
    print(f"数据集大小: {len(dataset)}")
    print(f"缓存信息: {dataset.get_cache_info()}")
    
    # 错误统计
    error_samples = [a for a in analyses if a['has_error']]
    print(f"错误样本数量: {len(error_samples)}/{len(analyses)}")
    
    if error_samples:
        print("错误类型:")
        for error_sample in error_samples:
            print(f"  - 样本 {error_sample['sample_idx']}: {error_sample['error_msg']}")
    
    # 正常样本统计
    normal_samples = [a for a in analyses if not a['has_error']]
    if normal_samples:
        print(f"\n正常样本数量: {len(normal_samples)}")
        
        # 点云统计
        point_counts = [a['num_points'] for a in normal_samples if 'num_points' in a]
        if point_counts:
            print(f"点云点数统计: 最小={min(point_counts)}, 最大={max(point_counts)}, 平均={np.mean(point_counts):.1f}")
        
        # 物体点统计
        object_ratios = [a['object_ratio'] for a in normal_samples if 'object_ratio' in a]
        if object_ratios:
            print(f"物体点比例: 最小={min(object_ratios):.3f}, 最大={max(object_ratios):.3f}, 平均={np.mean(object_ratios):.3f}")
        
        # 抓取数量统计
        grasp_counts = [a['num_grasps'] for a in normal_samples if 'num_grasps' in a]
        if grasp_counts:
            print(f"抓取数量: {set(grasp_counts)}")
        
        # SE3有效性统计
        valid_se3_counts = [a['num_valid_se3'] for a in normal_samples if 'num_valid_se3' in a]
        if valid_se3_counts:
            print(f"有效SE3矩阵数量: 最小={min(valid_se3_counts)}, 最大={max(valid_se3_counts)}, 平均={np.mean(valid_se3_counts):.1f}")
        
        # 网格统计
        vertex_counts = [a['num_vertices'] for a in normal_samples if 'num_vertices' in a]
        face_counts = [a['num_faces'] for a in normal_samples if 'num_faces' in a]
        if vertex_counts:
            print(f"网格顶点数: 最小={min(vertex_counts)}, 最大={max(vertex_counts)}, 平均={np.mean(vertex_counts):.1f}")
        if face_counts:
            print(f"网格面数: 最小={min(face_counts)}, 最大={max(face_counts)}, 平均={np.mean(face_counts):.1f}")

def visualize_data_distribution(analyses: List[Dict[str, Any]]):
    """可视化数据分布"""
    normal_samples = [a for a in analyses if not a['has_error']]
    if not normal_samples:
        print("没有正常样本用于可视化")
        return
    
    # 准备数据
    point_counts = [a['num_points'] for a in normal_samples if 'num_points' in a]
    object_ratios = [a['object_ratio'] for a in normal_samples if 'object_ratio' in a]
    valid_se3_counts = [a['num_valid_se3'] for a in normal_samples if 'num_valid_se3' in a]
    vertex_counts = [a['num_vertices'] for a in normal_samples if 'num_vertices' in a]
    face_counts = [a['num_faces'] for a in normal_samples if 'num_faces' in a]
    
    # 创建图表
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    fig.suptitle('SceneLeapPlus 数据集分布统计')
    
    # 点云点数分布
    if point_counts:
        axes[0, 0].hist(point_counts, bins=20, alpha=0.7)
        axes[0, 0].set_title('点云点数分布')
        axes[0, 0].set_xlabel('点数')
        axes[0, 0].set_ylabel('频次')
    
    # 物体点比例分布
    if object_ratios:
        axes[0, 1].hist(object_ratios, bins=20, alpha=0.7)
        axes[0, 1].set_title('物体点比例分布')
        axes[0, 1].set_xlabel('比例')
        axes[0, 1].set_ylabel('频次')
    
    # 有效SE3矩阵数量分布
    if valid_se3_counts:
        axes[0, 2].hist(valid_se3_counts, bins=20, alpha=0.7)
        axes[0, 2].set_title('有效SE3矩阵数量分布')
        axes[0, 2].set_xlabel('数量')
        axes[0, 2].set_ylabel('频次')
    
    # 网格顶点数分布
    if vertex_counts:
        axes[1, 0].hist(vertex_counts, bins=20, alpha=0.7)
        axes[1, 0].set_title('网格顶点数分布')
        axes[1, 0].set_xlabel('顶点数')
        axes[1, 0].set_ylabel('频次')
    
    # 网格面数分布
    if face_counts:
        axes[1, 1].hist(face_counts, bins=20, alpha=0.7)
        axes[1, 1].set_title('网格面数分布')
        axes[1, 1].set_xlabel('面数')
        axes[1, 1].set_ylabel('频次')
    
    # 清空最后一个子图
    axes[1, 2].axis('off')
    
    plt.tight_layout()
    plt.savefig('sceneleapplus_data_distribution.png')
    plt.close()
    print("数据分布图已保存为 sceneleapplus_data_distribution.png")

def test_data_processing_flow(config: Dict[str, Any]):
    """
    测试数据加载和处理流程
    1. 使用DataLoader加载数据
    2. 使用 process_hand_pose 和 process_hand_pose_test 处理数据
    3. 打印数据流变化
    """
    logger.info("\n" + "="*80)
    logger.info("开始测试数据加载和处理流程")
    logger.info("="*80)

    rot_type = 'r6d'  # Example rotation type
    mode = 'camera_centric_scene_mean_normalized'  # Example mode

    def check_and_log_range(name, part):
        """辅助函数，用于检查并记录张量部分的范围"""
        is_part_in_range = (part >= -1.0 - 1e-6).all() and (part <= 1.0 + 1e-6).all()
        logger.info(f"    - {name} 部分是否在 [-1, 1] 范围内: {is_part_in_range}")
        if not is_part_in_range:
            min_val, max_val = part.min(), part.max()
            logger.warning(f"      实际范围: [{min_val.item()}, {max_val.item()}]")
            logger.warning(f"      {name} 部分完整数据:\n{part}")

    # --- 训练阶段测试 ---
    logger.info("\n--- 训练阶段 (process_hand_pose) ---")
    try:
        train_dataset = create_dataset_from_config(config, mode="train")
        if len(train_dataset) == 0:
            logger.warning("训练数据集为空，跳过处理流程测试")
            return

        train_dataloader = DataLoader(
            train_dataset,
            batch_size=2,
            shuffle=False,
            collate_fn=SceneLeapPlusDatasetCached.collate_fn
        )

        logger.info(f"使用 rot_type='{rot_type}' 和 mode='{mode}' 进行处理")

        for i, data_batch in enumerate(train_dataloader):
            if i >= 2:  # 只测试前2个批次
                break
            
            logger.info(f"\n--- 训练批次 {i+1} ---")
            
            # 保存原始 se3 用于后续比较
            original_se3 = data_batch.get('se3').clone() if data_batch.get('se3') is not None else None

            # 打印处理前的数据形状
            logger.info("处理前数据形状:")
            hand_pose_before = data_batch.get('hand_model_pose')
            se3_before = data_batch.get('se3')
            scene_pc_before = data_batch.get('scene_pc')
            pos_prompt_before = data_batch.get('positive_prompt')
            neg_prompts_before = data_batch.get('negative_prompts')

            if hand_pose_before is not None:
                logger.info(f"  hand_model_pose: {hand_pose_before.shape}")
            if se3_before is not None:
                logger.info(f"  se3 (将被消耗): {se3_before.shape}")
            if scene_pc_before is not None:
                logger.info(f"  scene_pc: {scene_pc_before.shape}")
            if pos_prompt_before is not None:
                logger.info(f"  positive_prompt: list of {len(pos_prompt_before)} strings")
            if neg_prompts_before is not None and isinstance(neg_prompts_before, list):
                logger.info(f"  negative_prompts: list of {len(neg_prompts_before)} lists")

            # 处理数据
            processed_data = process_hand_pose(data_batch, rot_type=rot_type, mode=mode)
            
            # 打印处理后的数据形状和尺寸变化
            logger.info("处理后数据形状 (尺寸变化):")
            hand_pose_after = processed_data.get('hand_model_pose')
            norm_pose_after = processed_data.get('norm_pose')
            scene_pc_after = processed_data.get('scene_pc')
            pos_prompt_after = processed_data.get('positive_prompt')
            neg_prompts_after = processed_data.get('negative_prompts')

            # hand_model_pose
            if hand_pose_after is not None and hand_pose_before is not None:
                shape_change = f"(变化: {hand_pose_before.shape} -> {hand_pose_after.shape})" if hand_pose_before.shape != hand_pose_after.shape else "(无变化)"
                logger.info(f"  hand_model_pose: {hand_pose_after.shape} {shape_change}")
            
            # norm_pose (新生成)
            if norm_pose_after is not None:
                logger.info(f"  norm_pose (新生成): {norm_pose_after.shape}")
            
            # scene_pc
            if scene_pc_after is not None and scene_pc_before is not None:
                shape_change = f"(无变化)" if scene_pc_before.shape == scene_pc_after.shape else f"(变化: {scene_pc_before.shape} -> {scene_pc_after.shape})"
                logger.info(f"  scene_pc: {scene_pc_after.shape} {shape_change}")

            # Prompts
            if pos_prompt_after is not None:
                logger.info(f"  positive_prompt: list of {len(pos_prompt_after)} strings (无变化)")
            if neg_prompts_after is not None and isinstance(neg_prompts_after, list):
                logger.info(f"  negative_prompts: list of {len(neg_prompts_after)} lists (无变化)")

            # --- 新增测试 1: 反归一化和范围检查 ---
            logger.info("--- 测试 1: 反归一化和范围检查 ---")
            try:
                # 1a. 反归一化
                denormalized_pose = denorm_hand_pose_robust(norm_pose_after, rot_type=rot_type, mode=mode)
                
                # 1b. 检查反归一化结果是否与处理后的手部姿态接近
                are_denorm_close = torch.allclose(denormalized_pose, hand_pose_after, atol=1e-5)
                logger.info(f"  反归一化姿态与原始姿态是否接近: {are_denorm_close}")
                if not are_denorm_close:
                    diff = torch.abs(denormalized_pose - hand_pose_after).max()
                    logger.warning(f"    最大差异: {diff.item()}")

                # 1c. 分部分检查 norm_pose 是否在 [-1, 1] 范围内
                logger.info("  分部分检查 norm_pose 范围:")
                rot_dim = ROT_DIM_DICT[rot_type]
                trans_part = norm_pose_after[..., :3]
                param_part = norm_pose_after[..., 3:3+JOINT_ANGLE_DIM]
                rot_part = norm_pose_after[..., 3+JOINT_ANGLE_DIM:]
                
                check_and_log_range("平移 (X)", trans_part[..., 0])
                check_and_log_range("平移 (Y)", trans_part[..., 1])
                check_and_log_range("平移 (Z)", trans_part[..., 2])
                check_and_log_range("关节角度 (Joint Angles)", param_part)
                check_and_log_range("旋转 (Rotation)", rot_part)

            except Exception as e:
                logger.error(f"  测试 1 执行失败: {e}", exc_info=True)


            # --- 新增测试 2: SE(3) 转换一致性检查 ---
            logger.info("--- 测试 2: SE(3) 转换一致性检查 ---")
            if rot_type == 'map':
                logger.info(f"  跳过 SE(3) 转换测试，因为 rot_type 为 'map'，其平移部分经过了特殊处理。")
            elif original_se3 is not None:
                try:
                    # 2a. 分解 hand_pose_after 以获取旋转和平移
                    B, N, D = hand_pose_after.shape
                    decomposed_trans, decomposed_rot_mat, _ = decompose_hand_pose(
                        hand_pose_after.reshape(B * N, D), rot_type=rot_type
                    )
                    
                    # 2b. 从分解的部分重建 SE(3) 矩阵
                    reconstructed_se3 = torch.eye(4, device=decomposed_trans.device, dtype=decomposed_trans.dtype).unsqueeze(0).repeat(B * N, 1, 1)
                    reconstructed_se3[:, :3, :3] = decomposed_rot_mat
                    reconstructed_se3[:, :3, 3] = decomposed_trans

                    # 2c. 将原始 se3 展平以便比较
                    original_se3_flat = original_se3.reshape(B * N, 4, 4)

                    # 2d. 比较重建的 SE(3) 和原始的 SE(3)
                    are_se3_close = torch.allclose(reconstructed_se3, original_se3_flat, atol=1e-5)
                    logger.info(f"  重建的 SE(3) 与原始 SE(3) 是否接近: {are_se3_close}")
                    if not are_se3_close:
                        diff = torch.abs(reconstructed_se3 - original_se3_flat).max()
                        logger.warning(f"    最大差异: {diff.item()}")

                except Exception as e:
                    logger.error(f"  测试 2 执行失败: {e}", exc_info=True)

    except Exception as e:
        logger.error(f"训练阶段处理流程测试失败: {e}", exc_info=True)


    # --- 验证阶段测试 ---
    logger.info("\n--- 验证阶段 (process_hand_pose_test) ---")
    try:
        val_dataset = create_dataset_from_config(config, mode="val")
        if len(val_dataset) == 0:
            logger.warning("验证数据集为空，跳过处理流程测试")
            return

        val_dataloader = DataLoader(
            val_dataset,
            batch_size=2,
            shuffle=False,
            collate_fn=SceneLeapPlusDatasetCached.collate_fn
        )
        
        logger.info(f"使用 rot_type='{rot_type}' 和 mode='{mode}' 进行处理")

        for i, data_batch in enumerate(val_dataloader):
            if i >= 2: # 只测试前2个批次
                break

            logger.info(f"\n--- 验证批次 {i+1} ---")
            
            # 保存原始 se3 用于后续比较
            original_se3 = data_batch.get('se3')
            if isinstance(original_se3, torch.Tensor):
                original_se3 = original_se3.clone()

            # 打印处理前的数据形状
            logger.info("处理前数据形状:")
            hand_pose_before = data_batch.get('hand_model_pose')
            se3_before = data_batch.get('se3')
            scene_pc_before = data_batch.get('scene_pc')
            pos_prompt_before = data_batch.get('positive_prompt')
            neg_prompts_before = data_batch.get('negative_prompts')

            # hand_model_pose & se3
            if isinstance(hand_pose_before, list):
                if hand_pose_before and all(isinstance(t, torch.Tensor) for t in hand_pose_before):
                    logger.info(f"  hand_model_pose: list of {len(hand_pose_before)} tensors, e.g., {hand_pose_before[0].shape}")
            elif hasattr(hand_pose_before, 'shape'):
                logger.info(f"  hand_model_pose: {hand_pose_before.shape}")

            if isinstance(se3_before, list):
                if se3_before and all(isinstance(t, torch.Tensor) for t in se3_before):
                    logger.info(f"  se3 (将被消耗): list of {len(se3_before)} tensors, e.g., {se3_before[0].shape}")
            elif hasattr(se3_before, 'shape'):
                logger.info(f"  se3 (将被消耗): {se3_before.shape}")

            # scene_pc & prompts
            if scene_pc_before is not None and isinstance(scene_pc_before, list):
                logger.info(f"  scene_pc: list of {len(scene_pc_before)} tensors")
            if pos_prompt_before is not None and isinstance(pos_prompt_before, list):
                logger.info(f"  positive_prompt: list of {len(pos_prompt_before)} strings")
            if neg_prompts_before is not None and isinstance(neg_prompts_before, list):
                logger.info(f"  negative_prompts: list of {len(neg_prompts_before)} lists")


            # 处理数据
            processed_data = process_hand_pose_test(data_batch, rot_type=rot_type, mode=mode)
            
            # 打印处理后的数据形状和尺寸变化
            logger.info("处理后数据形状 (尺寸变化):")
            hand_pose_after = processed_data.get('hand_model_pose')
            norm_pose_after = processed_data.get('norm_pose')
            scene_pc_after = processed_data.get('scene_pc')
            pos_prompt_after = processed_data.get('positive_prompt')
            neg_prompts_after = processed_data.get('negative_prompts')

            # hand_model_pose
            if isinstance(hand_pose_after, list):
                if hand_pose_after and all(isinstance(t, torch.Tensor) for t in hand_pose_after):
                    logger.info(f"  hand_model_pose: list of {len(hand_pose_after)} tensors, e.g., {hand_pose_after[0].shape} (结构变化)")
            elif hasattr(hand_pose_after, 'shape'):
                shape_change = f"(变化: {hand_pose_before.shape} -> {hand_pose_after.shape})" if hasattr(hand_pose_before, 'shape') and hand_pose_before.shape != hand_pose_after.shape else "(无变化)"
                logger.info(f"  hand_model_pose: {hand_pose_after.shape} {shape_change}")

            # norm_pose (新生成)
            if isinstance(norm_pose_after, list):
                if norm_pose_after and all(isinstance(t, torch.Tensor) for t in norm_pose_after):
                    logger.info(f"  norm_pose (新生成): list of {len(norm_pose_after)} tensors, e.g., {norm_pose_after[0].shape}")
            elif hasattr(norm_pose_after, 'shape'):
                 logger.info(f"  norm_pose (新生成): {norm_pose_after.shape}")

            # scene_pc
            if scene_pc_after is not None and isinstance(scene_pc_after, list):
                 logger.info(f"  scene_pc: list of {len(scene_pc_after)} tensors (无变化)")

            # prompts
            if pos_prompt_after is not None and isinstance(pos_prompt_after, list):
                logger.info(f"  positive_prompt: list of {len(pos_prompt_after)} strings (无变化)")
            if neg_prompts_after is not None and isinstance(neg_prompts_after, list):
                logger.info(f"  negative_prompts: list of {len(neg_prompts_after)} lists (无变化)")

            # 如果数据不是以list形式返回的，则执行详细的数值检查
            if isinstance(norm_pose_after, torch.Tensor):
                # --- 新增测试 1: 反归一化和范围检查 ---
                logger.info("--- 测试 1: 反归一化和范围检查 ---")
                try:
                    denormalized_pose = denorm_hand_pose_robust(norm_pose_after, rot_type=rot_type, mode=mode)
                    are_denorm_close = torch.allclose(denormalized_pose, hand_pose_after, atol=1e-5)
                    logger.info(f"  反归一化姿态与原始姿态是否接近: {are_denorm_close}")

                    # 分部分检查 norm_pose 是否在 [-1, 1] 范围内
                    logger.info("  分部分检查 norm_pose 范围:")
                    rot_dim = ROT_DIM_DICT[rot_type]
                    trans_part = norm_pose_after[..., :3]
                    param_part = norm_pose_after[..., 3:3+JOINT_ANGLE_DIM]
                    rot_part = norm_pose_after[..., 3+JOINT_ANGLE_DIM:]

                    check_and_log_range("平移 (X)", trans_part[..., 0])
                    check_and_log_range("平移 (Y)", trans_part[..., 1])
                    check_and_log_range("平移 (Z)", trans_part[..., 2])
                    check_and_log_range("关节角度 (Joint Angles)", param_part)
                    check_and_log_range("旋转 (Rotation)", rot_part)
                except Exception as e:
                    logger.error(f"  测试 1 执行失败: {e}", exc_info=True)

                # --- 新增测试 2: SE(3) 转换一致性检查 ---
                logger.info("--- 测试 2: SE(3) 转换一致性检查 ---")
                if rot_type == 'map':
                    logger.info(f"  跳过 SE(3) 转换测试 (rot_type is 'map')")
                elif original_se3 is not None and isinstance(original_se3, torch.Tensor):
                    try:
                        B, N, D = hand_pose_after.shape
                        decomposed_trans, decomposed_rot_mat, _ = decompose_hand_pose(
                            hand_pose_after.reshape(B * N, D), rot_type=rot_type
                        )
                        reconstructed_se3 = torch.eye(4, device=decomposed_trans.device, dtype=decomposed_trans.dtype).unsqueeze(0).repeat(B * N, 1, 1)
                        reconstructed_se3[:, :3, :3] = decomposed_rot_mat
                        reconstructed_se3[:, :3, 3] = decomposed_trans
                        original_se3_flat = original_se3.reshape(B * N, 4, 4)
                        are_se3_close = torch.allclose(reconstructed_se3, original_se3_flat, atol=1e-5)
                        logger.info(f"  重建的 SE(3) 与原始 SE(3) 是否接近: {are_se3_close}")
                    except Exception as e:
                        logger.error(f"  测试 2 执行失败: {e}", exc_info=True)
            else:
                logger.info("--- 跳过详细数值检查 (数据为 list 格式) ---")

    except Exception as e:
        logger.error(f"验证阶段处理流程测试失败: {e}", exc_info=True)


def main():
    """主函数"""
    print("开始 SceneLeapPlus 缓存数据集测试和分析...")
    
    # 加载配置
    config_path = project_root / "config" / "data" / "sceneleapplus.yaml"
    if not config_path.exists():
        logger.error(f"配置文件不存在: {config_path}")
        return
    
    config = load_config(str(config_path))
    logger.info(f"加载配置文件: {config_path}")
    
    # 创建训练数据集
    logger.info("创建训练数据集...")
    start_time = time.time()
    
    try:
        train_dataset = create_dataset_from_config(config, mode="train")
        creation_time = time.time() - start_time
        logger.info(f"数据集创建完成，耗时: {creation_time:.2f}秒")
        
        print(f"训练数据集大小: {len(train_dataset)}")
        
        # 分析前几个样本
        num_samples_to_analyze = min(5, len(train_dataset))
        logger.info(f"分析前 {num_samples_to_analyze} 个样本...")
        
        analyses = []
        for i in range(num_samples_to_analyze):
            logger.info(f"加载样本 {i}...")
            sample_start_time = time.time()
            
            try:
                sample = train_dataset[i]
                sample_time = time.time() - sample_start_time
                logger.info(f"样本 {i} 加载完成，耗时: {sample_time:.3f}秒")
                
                analysis = analyze_sample_data(sample, i)
                analyses.append(analysis)
                
                # 打印样本详细信息
                print(f"\n样本 {i} 详细信息:")
                for key, value in analysis.items():
                    if key != 'sample_idx':
                        print(f"  {key}: {value}")
                        
            except Exception as e:
                logger.error(f"加载样本 {i} 时出错: {e}")
                analyses.append({
                    'sample_idx': i,
                    'has_error': True,
                    'error_msg': str(e)
                })
        
        # 打印数据集摘要
        print_dataset_summary(train_dataset, analyses)
        
        # 可视化数据分布
        # visualize_data_distribution(analyses)
        
        # 测试缓存信息
        cache_info = train_dataset.get_cache_info()
        print(f"\n缓存信息详情:")
        for key, value in cache_info.items():
            print(f"  {key}: {value}")
            
        # 运行数据处理流程测试
        test_data_processing_flow(config)
            
    except Exception as e:
        logger.error(f"创建数据集时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()