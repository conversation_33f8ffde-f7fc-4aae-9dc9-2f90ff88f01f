"""
SceneLeapPlus Cached Dataset Implementation
Efficient HDF5-based cached version supporting fixed number of grasps per sample
"""

import os
import json
import torch
import numpy as np
import h5py
import hashlib
import logging
import atexit
import time
from typing import Optional, List, Dict, Any, Union
from tqdm import tqdm

try:
    from datasets.sceneleapplus_dataset import SceneLeapPlusDataset
    from datasets.utils.common_utils import CameraInfo
    from datasets.utils.distributed_utils import (
        is_distributed_training,
        is_main_process,
        distributed_barrier,
        get_rank_info,
        ensure_directory_exists,
        should_create_cache,
        get_distributed_info
    )
    from datasets.utils.cache_utils import (
        wait_for_file,
        generate_cache_filename,
        validate_cache_file,
        check_cache_health,
        get_cache_directory,
        cleanup_cache_file,
        get_cache_info,
        log_cache_status,
        CacheManager
    )
    from datasets.utils.hdf5_utils import (
        save_value_to_group,
        load_item_from_cache,
        load_dataset_from_group,
        create_error_group,
        create_data_group,
        get_default_cache_keys,
        get_default_error_values
    )
    from datasets.utils.error_utils import (
        log_dataset_warning,
        handle_loading_exception
    )
    from datasets.utils.dataset_config import CachedDatasetConfig
    from datasets.utils.data_formatters import DataFormatter, ForMatchDataFormatter
    from datasets.utils.collate_utils import BatchCollator
    from datasets.utils.constants import (
        DEFAULT_MODE, DEFAULT_MAX_GRASPS_PER_OBJECT, DEFAULT_MESH_SCALE,
        DEFAULT_NUM_NEG_PROMPTS, DEFAULT_ENABLE_CROPPING, DEFAULT_MAX_POINTS,
        DEFAULT_CACHE_VERSION, DEFAULT_FORMATCH_CACHE_VERSION
    )
    # Import base cached dataset class
    from datasets.sceneleappro_cached import _BaseCachedDataset
except ImportError:
    logging.warning("Could not import SceneLeapPlusDataset. Using dummy implementation for demonstration.")


class SceneLeapPlusDatasetCached(_BaseCachedDataset, SceneLeapPlusDataset):
    """
    Cached version of SceneLeapPlusDataset using new infrastructure.

    Features:
    - Support for 6D point cloud data (xyz+rgb)
    - Support for text conditions (positive and negative prompts)
    - Support for fixed number of grasps per sample (num_grasps, 23) and SE3 matrices (num_grasps, 4, 4)
    - Support for object mesh data (vertices and faces)
    - Efficient HDF5 cache storage with fixed-size data handling
    - Distributed training support
    - Memory-optimized data loading for parallel multi-grasp learning
    """

    def __init__(
        self,
        root_dir: str,
        succ_grasp_dir: str,
        obj_root_dir: str,
        num_grasps: int = 8,
        mode: str = DEFAULT_MODE,
        max_grasps_per_object: Optional[int] = DEFAULT_MAX_GRASPS_PER_OBJECT,
        mesh_scale: float = DEFAULT_MESH_SCALE,
        num_neg_prompts: int = DEFAULT_NUM_NEG_PROMPTS,
        enable_cropping: bool = DEFAULT_ENABLE_CROPPING,
        max_points: int = DEFAULT_MAX_POINTS,
        grasp_sampling_strategy: str = "random",
        cache_version: str = "v1.0_plus",
        cache_mode: str = "train"
    ):
        """
        Initialize cached SceneLeapPlus dataset with identical signature to preserve API.

        Args:
            root_dir: Dataset root directory
            succ_grasp_dir: Directory containing successful grasp data
            obj_root_dir: Directory containing object mesh data
            num_grasps: Fixed number of grasps to return per sample
            mode: Coordinate system mode
            max_grasps_per_object: Maximum number of grasps per object
            mesh_scale: Scale factor for object meshes
            num_neg_prompts: Number of negative prompts
            enable_cropping: Whether to enable point cloud cropping
            max_points: Maximum number of point cloud points
            grasp_sampling_strategy: Strategy for grasp sampling ("random", "first_n", "repeat", "farthest_point", "nearest_point")
            cache_version: Cache version number
            cache_mode: Cache mode - "train" for training or "val" for validation
        """
        logging.info("SceneLeapPlusDatasetCached: Starting initialization...")

        # Store SceneLeapPlus specific parameters
        self.num_grasps = num_grasps
        self.grasp_sampling_strategy = grasp_sampling_strategy
        self.cache_mode = cache_mode

        # Initialize SceneLeapPlusDataset directly first
        SceneLeapPlusDataset.__init__(
            self,
            root_dir=root_dir,
            succ_grasp_dir=succ_grasp_dir,
            obj_root_dir=obj_root_dir,
            num_grasps=num_grasps,
            mode=mode,
            max_grasps_per_object=max_grasps_per_object,
            mesh_scale=mesh_scale,
            num_neg_prompts=num_neg_prompts,
            enable_cropping=enable_cropping,
            max_points=max_points,
            grasp_sampling_strategy=grasp_sampling_strategy
        )

        # Create configuration using new infrastructure (without SceneLeapPlus specific parameters)
        config = CachedDatasetConfig(
            root_dir=root_dir,
            succ_grasp_dir=succ_grasp_dir,
            obj_root_dir=obj_root_dir,
            mode=mode,
            max_grasps_per_object=max_grasps_per_object,
            mesh_scale=mesh_scale,
            num_neg_prompts=num_neg_prompts,
            enable_cropping=enable_cropping,
            max_points=max_points,
            cache_version=cache_version,
            cache_mode=cache_mode
        )

        # SceneLeapPlus specific parameters are already stored above (lines 119-120)

        # Set up cache system manually (similar to _BaseCachedDataset)
        self.config = config
        self.num_items = len(self.data)  # Use the length from SceneLeapPlusDataset
        self._setup_cache_system()

        # Store additional attributes for backward compatibility
        self.succ_grasp_dir = succ_grasp_dir
        self.obj_root_dir = obj_root_dir
        self.max_grasps_per_object = max_grasps_per_object
        self.mesh_scale = mesh_scale
        self.num_neg_prompts = num_neg_prompts
        self.enable_cropping = enable_cropping
        self.max_points = max_points
        self.cache_version = cache_version
        self.coordinate_system_mode = mode

        # Create cache if needed
        self._ensure_cache_populated()

    def _setup_cache_system(self) -> None:
        """
        Setup cache manager and file handling.
        """
        if self.num_items > 0:
            # Create cache manager
            self.cache_manager = CacheManager(self.config, self.num_items)

            # Setup cache
            self.hf, self.cache_loaded = self.cache_manager.setup_cache()

            # Register cleanup
            atexit.register(self._cleanup)

            logging.info(f"SceneLeapPlusDatasetCached: Cache system setup completed. Loaded: {self.cache_loaded}")
        else:
            logging.warning("SceneLeapPlusDatasetCached: Parent dataset found 0 items. Cache will be empty.")
            self.cache_manager = None
            self.hf = None
            self.cache_loaded = False

    def _is_cache_available(self) -> bool:
        """
        Check cache availability.

        Returns:
            bool: True if cache is available
        """
        if self.cache_manager is None:
            return False
        return self.cache_manager.is_loaded()

    def _periodic_health_check(self, idx: int) -> None:
        """
        Perform periodic cache health checks.

        Args:
            idx: Current item index
        """
        if self.cache_manager is not None:
            self.cache_manager.periodic_health_check(idx)

    def _cleanup(self) -> None:
        """
        Resource cleanup.
        """
        if self.cache_manager is not None:
            self.cache_manager.cleanup()
            self.cache_manager = None
        self.hf = None
        self.cache_loaded = False
        logging.info("SceneLeapPlusDatasetCached: Cleanup completed")

    def get_cache_info(self) -> Dict[str, Any]:
        """
        Get cache information.

        Returns:
            Dict[str, Any]: Cache information dictionary
        """
        if self.cache_manager is not None:
            return self.cache_manager.get_cache_info()
        else:
            return {
                'cache_loaded': False,
                'num_items': self.num_items if hasattr(self, 'num_items') else 0,
                'error': 'Cache manager not initialized'
            }

    def _ensure_cache_populated(self) -> None:
        """Ensure cache is populated with data if it exists but is empty."""
        if self.cache_manager is not None and not self.cache_loaded:
            # Cache file exists but is not loaded (probably empty)
            # Need to populate it with data
            self.cache_manager.create_cache(self._create_cache_data)

            # Update our local state to match the cache manager
            self.hf = self.cache_manager.get_file_handle()
            self.cache_loaded = self.cache_manager.is_loaded()

            logging.info(f"SceneLeapPlusDatasetCached: Cache populated and loaded. Status: {self.cache_loaded}")

    def _create_cache_data(self, cache_path: str, num_items: int) -> None:
        """
        Create cache file and populate it with SceneLeapPlus data.

        Args:
            cache_path: Path to cache file
            num_items: Number of items to cache
        """
        logging.info(f"SceneLeapPlusDatasetCached: Populating cache with {num_items} items...")

        # Get cache keys for SceneLeapPlus dataset (includes additional fields)
        keys_to_cache = self._get_sceneleapplus_cache_keys()
        default_error_values = self._get_sceneleapplus_default_error_values()

        with h5py.File(cache_path, 'w') as hf:
            for idx in tqdm(range(num_items), desc=f"Caching SceneLeapPlus data"):
                try:
                    # Call parent class __getitem__ to get fully processed data
                    full_return_dict = SceneLeapPlusDataset.__getitem__(self, idx)

                    if 'error' in full_return_dict:
                        # Handle error case
                        error_msg = str(full_return_dict.get('error', 'Unknown error'))
                        # Use default values for error case
                        error_values = {key: full_return_dict.get(key, default_error_values[key]) for key in keys_to_cache}
                        self._create_sceneleapplus_error_group(hf, idx, error_msg, error_values)
                    else:
                        # Handle normal data
                        self._create_sceneleapplus_data_group(hf, idx, full_return_dict, keys_to_cache)

                except Exception as e:
                    logging.error(f"SceneLeapPlusDatasetCached: Error processing item {idx}: {e}")
                    # Create error group for processing failure
                    self._create_sceneleapplus_error_group(hf, idx, f"Cache creation error: {str(e)}")

        logging.info(f"SceneLeapPlusDatasetCached: Cache creation completed.")

    def _get_sceneleapplus_cache_keys(self) -> List[str]:
        """
        Get list of keys to cache for SceneLeapPlus training.

        Returns:
            list: Cache keys for SceneLeapPlus dataset based on cache_mode
        """
        # Base keys that are always cached
        base_keys = [
            'scene_pc',           # 6D point cloud data (xyz+rgb)
            'object_mask',        # Object mask for point cloud
            'hand_model_pose',    # Fixed number of hand poses (num_grasps, 23)
            'se3',               # Fixed number of SE3 matrices (num_grasps, 4, 4)
            'positive_prompt',    # Positive prompt string
            'negative_prompts',   # List of negative prompt strings
            'obj_verts',         # Object vertices (V, 3)
            'obj_faces'          # Object faces (F, 3)
        ]
        
        # Additional keys for different cache modes
        if self.cache_mode == "val" or self.cache_mode == "test":
            # Add identification fields for validation/testing
            base_keys.extend([
                'obj_code',                        # Object code
                'scene_id',                        # Scene identifier
                'category_id_from_object_index',   # Category ID
                'depth_view_index'                 # Depth view index
            ])
        
        return base_keys

    def _get_sceneleapplus_default_error_values(self) -> Dict[str, Any]:
        """
        Get default values for error cases in SceneLeapPlus dataset.

        Returns:
            dict: Default error values based on cache_mode and num_grasps
        """
        default_values = {
            'scene_pc': torch.zeros((0, 6), dtype=torch.float32),  # 6D for xyz+rgb
            'object_mask': torch.zeros((0), dtype=torch.bool),
            'hand_model_pose': torch.zeros((self.num_grasps, 23), dtype=torch.float32),  # Fixed size
            'se3': torch.zeros((self.num_grasps, 4, 4), dtype=torch.float32),  # Fixed size
            'obj_verts': torch.zeros((0, 3), dtype=torch.float32),
            'obj_faces': torch.zeros((0, 3), dtype=torch.long),
            'positive_prompt': 'error_object',
            'negative_prompts': [''] * self.num_neg_prompts
        }
        
        # Additional fields for validation/testing
        if self.cache_mode == "val" or self.cache_mode == "test":
            default_values.update({
                'obj_code': 'unknown',
                'scene_id': 'unknown',
                'category_id_from_object_index': -1,
                'depth_view_index': -1
            })
        
        return default_values

    def _create_sceneleapplus_data_group(self, hf: h5py.File, idx: int, data_dict: Dict[str, Any], keys_to_cache: List[str]):
        """Create HDF5 group for normal SceneLeapPlus data item."""
        try:
            group = hf.create_group(str(idx))
            group.create_dataset('is_error', data=False)

            for key in keys_to_cache:
                if key in data_dict:
                    value = data_dict[key]
                    self._save_sceneleapplus_value_to_group(group, key, value)
                else:
                    logging.warning(f"SceneLeapPlusDatasetCached: Key '{key}' not found in data_dict for item {idx}")

        except Exception as e:
            logging.error(f"Error creating SceneLeapPlus data group for item {idx}: {e}")

    def _create_sceneleapplus_error_group(self, hf: h5py.File, idx: int, error_msg: str, error_values: Optional[Dict[str, Any]] = None):
        """Create HDF5 group for SceneLeapPlus error case."""
        try:
            group = hf.create_group(str(idx))
            group.create_dataset('is_error', data=True)
            group.create_dataset('error_msg', data=error_msg.encode('utf-8'))

            if error_values is None:
                error_values = self._get_sceneleapplus_default_error_values()

            for key, value in error_values.items():
                self._save_sceneleapplus_value_to_group(group, key, value)

        except Exception as e:
            logging.error(f"Error creating SceneLeapPlus error group for item {idx}: {e}")

    def _save_sceneleapplus_value_to_group(self, group: h5py.Group, key: str, value: Any):
        """Save value to HDF5 group with SceneLeapPlus-specific handling."""
        try:
            if isinstance(value, torch.Tensor):
                # Convert tensor to numpy for HDF5 storage
                numpy_value = value.detach().cpu().numpy()
                group.create_dataset(key, data=numpy_value, compression='gzip')
            elif isinstance(value, np.ndarray):
                group.create_dataset(key, data=value, compression='gzip')
            elif isinstance(value, str):
                # Store string as UTF-8 encoded bytes
                group.create_dataset(key, data=value.encode('utf-8'))
            elif isinstance(value, list):
                if key == 'negative_prompts':
                    # Handle list of strings for negative prompts
                    encoded_prompts = [prompt.encode('utf-8') for prompt in value]
                    group.create_dataset(key, data=encoded_prompts)
                else:
                    # Handle other list types
                    group.create_dataset(key, data=value)
            elif isinstance(value, (int, float)):
                # Handle scalar values
                group.create_dataset(key, data=value)
            else:
                # Try to store as-is
                group.create_dataset(key, data=value)

        except Exception as e:
            logging.error(f"Error saving SceneLeapPlus value for key '{key}': {e}")
            # Store a placeholder for failed values
            if key in ['scene_pc', 'hand_model_pose', 'se3', 'obj_verts', 'obj_faces']:
                if key == 'hand_model_pose':
                    group.create_dataset(key, data=np.zeros((self.num_grasps, 23)), compression='gzip')
                elif key == 'se3':
                    group.create_dataset(key, data=np.zeros((self.num_grasps, 4, 4)), compression='gzip')
                else:
                    group.create_dataset(key, data=np.array([]), compression='gzip')
            elif key == 'positive_prompt':
                group.create_dataset(key, data=b'error_object')
            elif key == 'negative_prompts':
                group.create_dataset(key, data=[b''] * self.num_neg_prompts)
            elif key in ['obj_code', 'scene_id']:
                group.create_dataset(key, data=b'unknown')
            elif key in ['category_id_from_object_index', 'depth_view_index']:
                group.create_dataset(key, data=-1)

    def __getitem__(self, idx: int) -> Dict[str, Any]:
        """
        Retrieve data item - must return identical format.

        Returns key fields required for SceneLeapPlus training:
        - scene_pc: 6D point cloud data (xyz+rgb) - PointNet2 feature extraction
        - hand_model_pose: Fixed number of hand poses (num_grasps, 23) - for parallel multi-grasp training
        - se3: Fixed number of SE3 transformation matrices (num_grasps, 4, 4) - loss calculations
        - positive_prompt: Positive prompt - text condition
        - negative_prompts: Negative prompts list - negative prompt loss
        - obj_verts: Object vertices (V, 3) - mesh data
        - obj_faces: Object faces (F, 3) - mesh data
        """
        if idx < 0 or idx >= self.num_items:
            raise IndexError(f"Index {idx} out of bounds for dataset with length {self.num_items}")

        # Check cache availability using base class method
        if not self._is_cache_available():
            return self._get_cache_unavailable_error()

        try:
            # Perform periodic health check using base class method
            self._periodic_health_check(idx)

            # Load cached data using SceneLeapPlus-specific loader
            cached_item_data = self._load_sceneleapplus_item_from_cache(idx)
            return self._process_sceneleapplus_cached_data(cached_item_data, idx)

        except Exception as e:
            return self._handle_sceneleapplus_retrieval_error(idx, e)

    def _get_cache_unavailable_error(self) -> Dict[str, Any]:
        """Get error response when cache is unavailable."""
        rank_info = get_rank_info()
        logging.error(f"SceneLeapPlusDatasetCached: [{rank_info}] Cache not available.")

        default_values = self._get_sceneleapplus_default_error_values()
        default_values['error'] = "Cache is not available."
        return default_values

    def _load_sceneleapplus_item_from_cache(self, idx: int) -> Dict[str, Any]:
        """Load SceneLeapPlus item from HDF5 cache."""
        try:
            if self.hf is None:
                return {'error': "Cache file is not available"}

            group = self.hf[str(idx)]
            cached_data = {}

            # Check if this is an error item
            is_error = group['is_error'][()]

            if is_error:
                # Load error information
                if 'error_msg' in group:
                    error_msg = group['error_msg'][()].decode('utf-8')
                    cached_data['error'] = error_msg
                else:
                    cached_data['error'] = "Unknown cached error"

            # Load all cached fields
            cache_keys = self._get_sceneleapplus_cache_keys()
            for key in cache_keys:
                if key in group:
                    dataset = group[key]

                    if key in ['scene_pc', 'hand_model_pose', 'se3', 'obj_verts', 'obj_faces', 'object_mask']:
                        # Convert numpy arrays back to tensors
                        numpy_data = dataset[:]
                        if key == 'obj_faces':
                            cached_data[key] = torch.from_numpy(numpy_data).long()
                        elif key == 'object_mask':
                            cached_data[key] = torch.from_numpy(numpy_data).bool()
                        else:
                            cached_data[key] = torch.from_numpy(numpy_data).float()
                    elif key in ['positive_prompt', 'obj_code', 'scene_id']:
                        # Decode UTF-8 string
                        cached_data[key] = dataset[()].decode('utf-8')
                    elif key == 'negative_prompts':
                        # Decode list of UTF-8 strings
                        encoded_prompts = dataset[:]
                        cached_data[key] = [prompt.decode('utf-8') for prompt in encoded_prompts]
                    elif key in ['category_id_from_object_index', 'depth_view_index']:
                        # Handle integer values
                        cached_data[key] = int(dataset[()])
                    else:
                        cached_data[key] = dataset[()]

            return cached_data

        except Exception as e:
            logging.error(f"SceneLeapPlusDatasetCached: Error loading item {idx} from cache: {e}")
            return {'error': f"Cache loading error: {str(e)}"}

    def _process_sceneleapplus_cached_data(self, cached_item_data: Dict[str, Any], idx: int) -> Dict[str, Any]:
        """Process cached data and return formatted result."""
        if 'error' in cached_item_data:
            logging.warning(f"SceneLeapPlusDatasetCached: Returning cached error for index {idx}.")
            return self._format_sceneleapplus_error_data(cached_item_data)
        else:
            return self._format_sceneleapplus_normal_data(cached_item_data)

    def _format_sceneleapplus_error_data(self, cached_item_data: Dict[str, Any]) -> Dict[str, Any]:
        """Format error data with fallback values."""
        default_values = self._get_sceneleapplus_default_error_values()

        # Use cached values if available, otherwise use defaults
        result = {}
        for key in self._get_sceneleapplus_cache_keys():
            if key in cached_item_data:
                result[key] = cached_item_data[key]
            else:
                result[key] = default_values.get(key, None)

        # Add error message
        result['error'] = cached_item_data.get('error', 'Unknown error')
        return result

    def _format_sceneleapplus_normal_data(self, cached_item_data: Dict[str, Any]) -> Dict[str, Any]:
        """Format normal cached data."""
        # Return cached data as-is since it should already be in the correct format
        return cached_item_data

    def _handle_sceneleapplus_retrieval_error(self, idx: int, error: Exception) -> Dict[str, Any]:
        """Handle retrieval errors and return error response."""
        logging.error(f"SceneLeapPlusDatasetCached: Error retrieving item {idx}: {error}")
        default_values = self._get_sceneleapplus_default_error_values()
        default_values['error'] = f"Retrieval error: {str(error)}"
        return default_values

    def __len__(self) -> int:
        """Returns dataset size - unchanged."""
        return self.num_items

    @staticmethod
    def collate_fn(batch):
        """
        Collates a batch of items from SceneLeapPlusDatasetCached.
        Since each item already has fixed number of grasps, we can use standard tensor stacking.
        This is identical to the original SceneLeapPlusDataset.collate_fn.
        """
        return SceneLeapPlusDataset.collate_fn(batch)
