import os
import json
import torch
from torch.utils.data import Dataset
import numpy as np
import cv2
from pytorch3d.transforms import (quaternion_to_matrix, matrix_to_quaternion)
from typing import Optional, List, Dict, Any, Tuple
import random

from .utils.common_utils import (
    create_point_cloud_from_depth_image,
    CameraInfo
)
from .utils.io_utils import (
    load_object_mesh,
    load_scene_images
)
from .utils.pointcloud_utils import (
    add_rgb_to_pointcloud,
    map_2d_mask_to_3d_pointcloud,
    downsample_point_cloud_with_mask,
    crop_point_cloud_to_objects_with_mask
)
from .utils.mask_utils import (
    extract_object_mask
)
from .utils.transform_utils import (
    create_se3_matrix_from_pose,
    extract_object_name_from_code,
    generate_negative_prompts,
    get_camera_transform,
    get_specific_hand_pose
)
from .utils.error_utils import (
    log_dataset_warning,
    handle_loading_exception
)
from .utils.data_processing_utils import (
    load_and_process_hand_pose_data,
    load_scene_metadata,
    validate_dataset_configuration,
    collate_batch_data,
    collate_variable_grasps_batch,
    get_depth_view_indices_from_scene
)
from .utils.coordinate_transform_strategies import (
    TransformationData,
    create_transform_strategy
)

# Import base class
from .sceneleappro_dataset import _BaseLeapProDataset


class SceneLeapPlusDataset(_BaseLeapProDataset):
    """
    SceneLeapPlus dataset that returns fixed number of grasps per item for parallel multi-grasp learning.
    
    This dataset is designed to support parallel multi-grasp distribution learning architecture.
    Key differences from other datasets:
    - Returns fixed number of grasps (num_grasps) per sample
    - Data format: hand_model_pose [num_grasps, 23], se3 [num_grasps, 4, 4]
    - Each data item corresponds to one object in a scene view
    - Handles grasp sampling/padding when available grasps != num_grasps
    """

    def __init__(self, root_dir: str, succ_grasp_dir: str, obj_root_dir: str, 
                 num_grasps: int = 8, mode: str = "camera_centric",
                 max_grasps_per_object: Optional[int] = 200, mesh_scale: float = 0.1,
                 num_neg_prompts: int = 4, enable_cropping: bool = True, max_points: int = 10000,
                 grasp_sampling_strategy: str = "random"):
        """
        Initialize SceneLeapPlusDataset.

        Args:
            root_dir: Root directory containing scene data
            succ_grasp_dir: Directory containing successful grasp data
            obj_root_dir: Directory containing object mesh data
            num_grasps: Fixed number of grasps to return per sample
            mode: Coordinate frame mode ("camera_centric", "object_centric", "camera_centric_obj_mean_normalized")
            max_grasps_per_object: Maximum number of grasps per object (None for unlimited)
            mesh_scale: Scale factor for object meshes
            num_neg_prompts: Number of negative prompts to generate
            enable_cropping: Whether to enable point cloud cropping
            max_points: Maximum number of points in downsampled point cloud
            grasp_sampling_strategy: Strategy for grasp sampling ("random", "first_n", "repeat", "farthest_point", "nearest_point")
        """
        # Initialize base class
        super().__init__(root_dir, succ_grasp_dir, obj_root_dir, mode, max_grasps_per_object,
                         mesh_scale, num_neg_prompts, enable_cropping, max_points)
        
        # Store new parameters
        self.num_grasps = num_grasps
        self.grasp_sampling_strategy = grasp_sampling_strategy
        
        # Validate parameters
        if num_grasps <= 0:
            raise ValueError(f"num_grasps must be positive, got {num_grasps}")
        if grasp_sampling_strategy not in ["random", "first_n", "repeat", "farthest_point", "nearest_point"]:
            raise ValueError(f"Invalid grasp_sampling_strategy: {grasp_sampling_strategy}")
        
        # Filter hand_pose_data to keep only collision-free poses (similar to ForMatchSceneLeapProDataset)
        self._filter_collision_free_poses()
        
        # Build data index (one item per object per view)
        self.data = self._build_data_index()

    def _filter_collision_free_poses(self):
        """
        Filter hand_pose_data to keep only collision-free poses based on collision_free_grasp_info.
        This modifies self.hand_pose_data in place.
        """
        # Iterate over a copy of scene_ids in self.hand_pose_data in case scenes are removed
        for scene_id_hp, scene_poses_hp in list(self.hand_pose_data.items()):
            if scene_id_hp not in self.collision_free_grasp_info:
                # This scene has no collision-free info, so all its objects have 0 collision-free grasps
                for obj_code_hp in list(scene_poses_hp.keys()):
                    all_poses_for_obj = scene_poses_hp[obj_code_hp]
                    pose_dim = 23  # Default pose dimension
                    dtype = torch.float32
                    device = 'cpu'  # Default device
                    if isinstance(all_poses_for_obj, torch.Tensor) and all_poses_for_obj.numel() > 0:
                        if all_poses_for_obj.ndim == 2:
                            pose_dim = all_poses_for_obj.shape[1]
                        dtype = all_poses_for_obj.dtype
                        device = all_poses_for_obj.device
                    self.hand_pose_data[scene_id_hp][obj_code_hp] = torch.zeros((0, pose_dim), dtype=dtype, device=device)
                continue

            grasp_entries_for_scene = self.collision_free_grasp_info[scene_id_hp]
            obj_code_to_cf_indices = {}
            for obj_grasp_entry in grasp_entries_for_scene:
                obj_name = obj_grasp_entry.get('object_name')
                obj_uid = obj_grasp_entry.get('uid')
                if not obj_name or not obj_uid:
                    continue
                current_obj_code = f"{obj_name}_{obj_uid}"
                obj_code_to_cf_indices[current_obj_code] = obj_grasp_entry.get('collision_free_indices', [])

            for obj_code_hp, all_poses_for_obj in list(scene_poses_hp.items()):
                pose_dim = 23  # Default
                dtype = torch.float32
                device = 'cpu'  # Default
                if isinstance(all_poses_for_obj, torch.Tensor) and all_poses_for_obj.numel() > 0:
                    if all_poses_for_obj.ndim == 2:
                        pose_dim = all_poses_for_obj.shape[1]
                    dtype = all_poses_for_obj.dtype
                    device = all_poses_for_obj.device
                else:  # all_poses_for_obj is None or not a Tensor or an empty Tensor
                    self.hand_pose_data[scene_id_hp][obj_code_hp] = torch.zeros((0, pose_dim), dtype=dtype, device=device)
                    continue

                collision_free_indices = obj_code_to_cf_indices.get(obj_code_hp)

                if collision_free_indices is not None and len(collision_free_indices) > 0:
                    try:
                        cf_indices_tensor = torch.tensor(collision_free_indices, dtype=torch.long)
                        valid_mask = (cf_indices_tensor >= 0) & (cf_indices_tensor < all_poses_for_obj.shape[0])
                        valid_cf_indices = cf_indices_tensor[valid_mask]

                        if self.max_grasps_per_object is not None and len(valid_cf_indices) > self.max_grasps_per_object:
                            valid_cf_indices = valid_cf_indices[:self.max_grasps_per_object]

                        if len(valid_cf_indices) > 0:
                            self.hand_pose_data[scene_id_hp][obj_code_hp] = all_poses_for_obj.index_select(0, valid_cf_indices.to(all_poses_for_obj.device))
                        else:
                            self.hand_pose_data[scene_id_hp][obj_code_hp] = torch.zeros((0, pose_dim), dtype=dtype, device=device)
                    except Exception:  # Broad catch for safety during indexing
                        self.hand_pose_data[scene_id_hp][obj_code_hp] = torch.zeros((0, pose_dim), dtype=dtype, device=device)
                else:  # No collision_free_indices for this obj_code or an empty list
                    self.hand_pose_data[scene_id_hp][obj_code_hp] = torch.zeros((0, pose_dim), dtype=dtype, device=device)

    def __len__(self):
        return len(self.data)

    def _build_data_index(self):
        """
        Builds a data index where each item corresponds to an object in a scene view.
        Similar to ForMatchSceneLeapProDataset but for fixed number of grasps.
        """
        data_index = []
        for scene_dir_path_local in self.scene_dirs:
            scene_id = os.path.basename(scene_dir_path_local)
            current_scene_collision_info = self.collision_free_grasp_info.get(scene_id, [])

            # Get depth view indices
            depth_view_indices = get_depth_view_indices_from_scene(scene_dir_path_local)
            if not depth_view_indices:
                continue

            for obj_grasp_entry in current_scene_collision_info:
                obj_name = obj_grasp_entry.get('object_name')
                obj_uid = obj_grasp_entry.get('uid')
                category_id_for_masking = obj_grasp_entry.get('object_index')

                if not obj_name or not obj_uid or category_id_for_masking is None:
                    continue

                object_code = f"{obj_name}_{obj_uid}"

                # Check if we have hand poses for this object
                obj_poses_tensor = self.hand_pose_data.get(scene_id, {}).get(object_code)
                if obj_poses_tensor is None or obj_poses_tensor.shape[0] == 0:
                    continue  # No hand poses available for this object

                for depth_view_idx in depth_view_indices:
                    data_index.append({
                        'scene_id': scene_id,
                        'object_code': object_code,
                        'category_id_for_masking': category_id_for_masking,
                        'depth_view_index': depth_view_idx
                        # Note: 'grasp_npy_idx' is not included in item_data
                    })

        if not data_index:
            print("Warning: SceneLeapPlusDataset data_index is empty after build.")
        return data_index

    def _farthest_point_sampling(self, points: torch.Tensor, num_samples: int) -> torch.Tensor:
        """
        Farthest Point Sampling (FPS) algorithm for 3D points.
        
        Args:
            points: Input points tensor of shape (N, 3)
            num_samples: Number of points to sample
            
        Returns:
            Indices of sampled points, shape (num_samples,)
        """
        N = points.shape[0]
        device = points.device
        
        if N <= num_samples:
            return torch.arange(N, device=device)
            
        # Initialize with random first point
        sampled_indices = torch.zeros(num_samples, dtype=torch.long, device=device)
        sampled_indices[0] = torch.randint(0, N, (1,), device=device)
        
        # Distance matrix to keep track of minimum distances to sampled points
        distances = torch.full((N,), float('inf'), device=device)
        
        for i in range(1, num_samples):
            # Get the last sampled point
            last_sampled = sampled_indices[i-1]
            last_point = points[last_sampled]
            
            # Calculate distances from all points to the last sampled point
            current_distances = torch.norm(points - last_point.unsqueeze(0), dim=1)
            
            # Update minimum distances
            distances = torch.min(distances, current_distances)
            
            # Select the point with maximum distance to all previously sampled points
            sampled_indices[i] = torch.argmax(distances)
            
            # Set the distance of the newly sampled point to 0
            distances[sampled_indices[i]] = 0
            
        return sampled_indices
    
    def _nearest_point_sampling(self, points: torch.Tensor, num_samples: int) -> torch.Tensor:
        """
        Nearest Point Sampling (NPS) algorithm for 3D points.
        Randomly select one point, then find the nearest num_samples points to it.
        
        Args:
            points: Input points tensor of shape (N, 3)
            num_samples: Number of points to sample
            
        Returns:
            Indices of sampled points, shape (num_samples,)
        """
        N = points.shape[0]
        device = points.device
        
        if N <= num_samples:
            return torch.arange(N, device=device)
            
        # Randomly select the center point
        center_idx = torch.randint(0, N, (1,), device=device).item()
        center_point = points[center_idx]
        
        # Calculate distances from all points to the center point
        distances = torch.norm(points - center_point.unsqueeze(0), dim=1)
        
        # Get indices of the nearest num_samples points (including the center point itself)
        _, nearest_indices = torch.topk(distances, num_samples, largest=False)
        
        return nearest_indices
    
    def _sample_grasps_from_available(self, available_grasps: torch.Tensor) -> torch.Tensor:
        """
        Sample fixed number of grasps from available grasps using the specified strategy.

        Args:
            available_grasps: Available grasp poses tensor of shape (N, 23)

        Returns:
            Sampled grasp poses tensor of shape (num_grasps, 23)
        """
        num_available = available_grasps.shape[0]

        if num_available == 0:
            # No grasps available, return zero tensor
            return torch.zeros((self.num_grasps, 23), dtype=available_grasps.dtype, device=available_grasps.device)

        if num_available >= self.num_grasps:
            # More grasps available than needed, sample according to strategy
            if self.grasp_sampling_strategy == "random":
                indices = torch.randperm(num_available)[:self.num_grasps]
                return available_grasps[indices]
            elif self.grasp_sampling_strategy == "first_n":
                return available_grasps[:self.num_grasps]
            elif self.grasp_sampling_strategy == "farthest_point":
                # Use farthest point sampling based on translation parameters (first 3 dimensions)
                translation_points = available_grasps[:, :3]  # Extract translation parameters
                fps_indices = self._farthest_point_sampling(translation_points, self.num_grasps)
                return available_grasps[fps_indices]
            elif self.grasp_sampling_strategy == "nearest_point":
                # Use nearest point sampling based on translation parameters (first 3 dimensions)
                translation_points = available_grasps[:, :3]  # Extract translation parameters
                nps_indices = self._nearest_point_sampling(translation_points, self.num_grasps)
                return available_grasps[nps_indices]
            else:  # "repeat" strategy - shouldn't happen when num_available >= num_grasps, but handle anyway
                indices = torch.arange(self.num_grasps) % num_available
                return available_grasps[indices]
        else:
            # Fewer grasps available than needed, need to repeat/pad
            if self.grasp_sampling_strategy == "repeat":
                # Repeat grasps cyclically to fill num_grasps
                indices = torch.arange(self.num_grasps) % num_available
                return available_grasps[indices]
            elif self.grasp_sampling_strategy == "farthest_point":
                # For FPS with insufficient data, first select all available points,
                # then fill remaining slots with random sampling
                all_indices = torch.arange(num_available, device=available_grasps.device)
                remaining_needed = self.num_grasps - num_available
                additional_indices = torch.randint(0, num_available, (remaining_needed,), device=available_grasps.device)
                final_indices = torch.cat([all_indices, additional_indices])
                return available_grasps[final_indices]
            elif self.grasp_sampling_strategy == "nearest_point":
                # For NPS with insufficient data, first select all available points,
                # then fill remaining slots with random sampling
                all_indices = torch.arange(num_available, device=available_grasps.device)
                remaining_needed = self.num_grasps - num_available
                additional_indices = torch.randint(0, num_available, (remaining_needed,), device=available_grasps.device)
                final_indices = torch.cat([all_indices, additional_indices])
                return available_grasps[final_indices]
            else:  # "random" or "first_n" - use random sampling with replacement
                indices = torch.randint(0, num_available, (self.num_grasps,))
                return available_grasps[indices]

    def _get_fixed_number_hand_poses(self, scene_id: str, object_code: str) -> torch.Tensor:
        """
        Get fixed number of hand poses for a given scene and object code.

        Args:
            scene_id: Scene identifier
            object_code: Object code

        Returns:
            torch.Tensor: Fixed number of hand poses, shape (num_grasps, 23)
        """
        all_poses_tensor = self.hand_pose_data.get(scene_id, {}).get(object_code)
        default_pose_dim = 23

        if all_poses_tensor is None:
            return torch.zeros((self.num_grasps, default_pose_dim), dtype=torch.float32)

        if not isinstance(all_poses_tensor, torch.Tensor):
            return torch.zeros((self.num_grasps, default_pose_dim), dtype=torch.float32)

        if all_poses_tensor.ndim != 2 or all_poses_tensor.shape[1] != default_pose_dim:
            return torch.zeros((self.num_grasps, default_pose_dim), dtype=torch.float32)

        # Sample grasps using the specified strategy
        return self._sample_grasps_from_available(all_poses_tensor)

    def __getitem__(self, idx):
        """
        Get item for SceneLeapPlusDataset.
        Returns fixed number of grasps for a given object in a scene view.
        """
        if idx >= len(self.data):
            raise IndexError(f"Index {idx} out of bounds for SceneLeapPlusDataset with length {len(self.data)}")

        item_data = self.data[idx]

        try:
            # Load scene data
            scene_data = self._load_scene_data(item_data)
            if scene_data is None:
                return self._create_error_return_dict(item_data, "Failed to load scene data")

            # Process point cloud and masks
            processed_data = self._process_point_cloud_and_masks(scene_data, item_data)
            if processed_data is None:
                return self._create_error_return_dict(item_data, "Failed to process point cloud and masks")

            # Load fixed number of grasp poses and object mesh
            grasp_object_data = self._load_grasp_and_object_data_for_fixed_grasps(item_data)
            if grasp_object_data is None:
                return self._create_error_return_dict(item_data, "Failed to load grasp and object data")

            # Transform data by coordinate frame mode
            transformed_data = self._transform_data_by_mode(
                pc_cam_raw_xyz_rgb=processed_data['scene_pc_with_rgb'],
                grasps_omf=grasp_object_data['hand_pose_tensor'],
                obj_verts=grasp_object_data['obj_verts'],
                R_omf_to_cf_np=grasp_object_data['cam_R_model_to_camera'],
                t_omf_to_cf_np=grasp_object_data['cam_t_model_to_camera'],
                object_mask_np=processed_data['object_mask_np']
            )

            # Package final data for fixed number of grasps
            return self._package_data_for_fixed_grasps(item_data, transformed_data, processed_data['object_mask_np'], grasp_object_data['obj_faces'])

        except Exception as e:
            return self._create_error_return_dict(item_data, f"Unexpected error: {str(e)}")

    def _load_grasp_and_object_data_for_fixed_grasps(self, item_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Load grasp poses and object mesh data for fixed number of grasps.
        """
        scene_id = item_data['scene_id']
        object_code = item_data['object_code']
        category_id_for_masking = item_data['category_id_for_masking']
        depth_view_index = item_data['depth_view_index']

        # Get fixed number of hand poses for this object (num_grasps, 23)
        hand_pose_tensor = self._get_fixed_number_hand_poses(scene_id, object_code)

        # Get camera transformation
        scene_gt_for_view = self.scene_gt.get(scene_id, {}).get(str(depth_view_index), [])
        cam_R_model_to_camera, cam_t_model_to_camera = self._get_camera_transform(scene_gt_for_view, category_id_for_masking)

        # Load object mesh
        obj_verts, obj_faces = load_object_mesh(self.obj_root_dir, object_code, self.mesh_scale)
        if obj_verts is None or obj_faces is None:
            return None

        return {
            'hand_pose_tensor': hand_pose_tensor,
            'scene_gt_for_view': scene_gt_for_view,
            'cam_R_model_to_camera': cam_R_model_to_camera,
            'cam_t_model_to_camera': cam_t_model_to_camera,
            'obj_verts': obj_verts,
            'obj_faces': obj_faces
        }

    def _package_data_for_fixed_grasps(self, item_data: Dict[str, Any], transformed_data: Dict[str, Any],
                                     object_mask_np: np.ndarray, obj_faces: torch.Tensor) -> Dict[str, Any]:
        """Package transformed data into final return dictionary for fixed number of grasps."""
        return self._package_data_base(item_data, transformed_data, object_mask_np, obj_faces, is_batch=True)

    def _create_error_return_dict(self, item_data: Dict[str, Any], error_msg: str) -> Dict[str, Any]:
        """
        Create standardized error return dictionary for fixed grasp format.

        Args:
            item_data: Item data dictionary
            error_msg: Error message describing the failure

        Returns:
            Standardized error dictionary compatible with fixed grasp format
        """
        return self._create_error_return_dict_base(item_data, error_msg, is_batch=True)

    @staticmethod
    def collate_fn(batch):
        """
        Collates a batch of items from SceneLeapPlusDataset.
        Since each item already has fixed number of grasps, we can use standard tensor stacking.
        """
        if not batch:
            return {}

        # Filter out None items or non-dict items from the batch
        batch = [item for item in batch if isinstance(item, dict)]
        if not batch:
            return {}

        collated_output = {}

        # Get all keys from all items
        all_keys = set()
        for item_dict in batch:
            all_keys.update(item_dict.keys())

        # Determine expected shapes by iterating through the batch until all shapes are found
        expected_shapes = {}
        keys_to_find_shape = ['hand_model_pose', 'se3']
        for item_dict in batch:
            for key in keys_to_find_shape:
                # If we haven't found the shape for this key yet, try to get it from the current item
                if key not in expected_shapes and key in item_dict and isinstance(item_dict[key], torch.Tensor):
                    expected_shapes[key] = item_dict[key].shape
            
            # If we have found all the shapes we need, we can stop searching
            if all(key in expected_shapes for key in keys_to_find_shape):
                break

        for key in all_keys:
            # Note: We are getting a list of values for the current key from all items in the batch.
            # item_dict.get(key) will return None if the key is missing in an item.
            current_key_items = [item_dict.get(key) for item_dict in batch]

            if key in ['hand_model_pose', 'se3']:
                # These should already have fixed dimensions, so we can stack directly
                try:
                    # Filter out None values and ensure all tensors have the same shape
                    valid_tensors = []
                    expected_shape = expected_shapes.get(key)

                    for item in current_key_items:
                        # If the item is a valid tensor with the correct shape, use it
                        if isinstance(item, torch.Tensor) and item.shape == expected_shape:
                            valid_tensors.append(item)
                        else:
                            # Otherwise, create a zero tensor with the correct shape as a placeholder.
                            # This handles cases where an item in the batch is an error dict.
                            if expected_shape is not None:
                                # Determine dtype and device from the faulty item if possible, otherwise use defaults
                                dtype = item.dtype if isinstance(item, torch.Tensor) else torch.float32
                                device = item.device if isinstance(item, torch.Tensor) else 'cpu'
                                valid_tensors.append(torch.zeros(expected_shape, dtype=dtype, device=device))
                            else:
                                # This is a fallback for the rare case where no valid tensor was found in the entire batch
                                # to establish an expected_shape. The shape here is a guess.
                                if key == 'hand_model_pose':
                                    # Assuming a default shape if none could be determined
                                    valid_tensors.append(torch.zeros((4, 23), dtype=torch.float32))
                                else:  # se3
                                    # This was the source of the original error.
                                    # Now it should only be reached if the entire batch is invalid.
                                    valid_tensors.append(torch.zeros((4, 4, 4), dtype=torch.float32))

                    if valid_tensors:
                        collated_output[key] = torch.stack(valid_tensors)
                    else:
                        # This case is unlikely but handled for safety: if the batch was empty or all items were invalid.
                        if key == 'hand_model_pose':
                            collated_output[key] = torch.empty((0, 4, 23), dtype=torch.float32)
                        else:  # se3
                            collated_output[key] = torch.empty((0, 4, 4, 4), dtype=torch.float32)

                except (RuntimeError, TypeError, AttributeError):
                    # If stacking fails for any reason, fall back to returning a list of items for that key
                    collated_output[key] = current_key_items

            elif key in ['object_mask', 'obj_verts', 'obj_faces', 'positive_prompt', 'negative_prompts', 'error']:
                # Keep these as lists
                collated_output[key] = current_key_items
            else:
                # Default collation for other keys
                try:
                    collated_output[key] = torch.utils.data.dataloader.default_collate(current_key_items)
                except (RuntimeError, TypeError, AttributeError):
                    collated_output[key] = current_key_items  # Fallback to list

        return collated_output
